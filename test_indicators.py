#!/usr/bin/env python3
"""
Test script to verify MACD and Supertrend calculations using Nifty Index data
"""

import os
import pandas as pd
import pandas_ta as ta
from datetime import datetime, timedelta
from dotenv import load_dotenv
import numpy as np

# Load environment variables
load_dotenv()

# Get configuration from environment
MACD_FAST = int(os.getenv("MACD_FAST", 12))
MACD_SLOW = int(os.getenv("MACD_SLOW", 26))
MACD_SIGNAL = int(os.getenv("MACD_SIGNAL", 9))
ST_LENGTH = int(os.getenv("ST_LENGTH", 10))
ST_MULTIPLIER = float(os.getenv("ST_MULTIPLIER", 3.0))
WARMUP_DAYS = int(os.getenv("WARMUP_DAYS", 1))
INDEX_CSV_PATH = os.getenv("INDEX_CSV_PATH", "NIFTY 50_Index_one_minute.csv")

def load_nifty_index_csv(csv_path: str) -> pd.DataFrame:
    """
    Load an index CSV with columns like: date,open,high,low,close,volume
    Returns standardized DataFrame: ['datetime','Open','High','Low','Close','Volume'] sorted by datetime.
    """
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"Index CSV not found: {csv_path}")

    df = pd.read_csv(csv_path)
    # Normalize column names to lower
    df.columns = [c.strip().lower() for c in df.columns]

    rename_map = {
        "date": "datetime",
        "datetime": "datetime",
        "open": "Open",
        "high": "High",
        "low": "Low",
        "close": "Close",
        "volume": "Volume",
        "vol": "Volume",
    }
    df = df.rename(columns=rename_map)

    required = ["datetime", "Open", "High", "Low", "Close", "Volume"]
    missing = [c for c in required if c not in df.columns]
    if missing:
        raise ValueError(f"Index CSV is missing required columns: {missing}")

    df["datetime"] = pd.to_datetime(df["datetime"])
    df = df[required].sort_values("datetime").reset_index(drop=True)
    return df

def add_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Input df columns: ['datetime','Open','High','Low','Close','Volume'].
    Adds MACD, VWAP, and Supertrend columns (from pandas-ta).
    Ensures a DatetimeIndex during indicator computation (required by pandas-ta's VWAP),
    then resets back to a column.
    """
    df = df.copy()
    if len(df) < 30:
        print(f"Warning: Not enough rows to compute indicators (need >= 30, got {len(df)}). Returning as-is.")
        return df

    if "datetime" not in df.columns:
        raise ValueError("add_indicators expects a 'datetime' column")

    # Ensure datetime is proper dtype and sorted
    df["datetime"] = pd.to_datetime(df["datetime"], errors="coerce")
    df = df.dropna(subset=["datetime"]).sort_values("datetime")

    # Temporarily set DatetimeIndex for pandas-ta (VWAP requires it)
    df = df.set_index("datetime")
    
    print(f"DataFrame shape before indicators: {df.shape}")
    print(f"Date range: {df.index.min()} to {df.index.max()}")
    print("Sample data:")
    print(df.head())

    # Compute indicators
    print(f"\nComputing MACD with parameters: fast={MACD_FAST}, slow={MACD_SLOW}, signal={MACD_SIGNAL}")
    macd_df = ta.macd(df["Close"], fast=MACD_FAST, slow=MACD_SLOW, signal=MACD_SIGNAL)
    print("MACD columns:", macd_df.columns.tolist() if macd_df is not None else "None")
    
    print(f"\nComputing Supertrend with parameters: length={ST_LENGTH}, multiplier={ST_MULTIPLIER}")
    supertrend_df = ta.supertrend(df["High"], df["Low"], df["Close"], length=ST_LENGTH, multiplier=ST_MULTIPLIER)
    print("Supertrend columns:", supertrend_df.columns.tolist() if supertrend_df is not None else "None")

    # Join to original
    if macd_df is not None:
        df = df.join(macd_df)
    if supertrend_df is not None:
        df = df.join(supertrend_df)

    # Reset index back to a datetime column
    df = df.reset_index().rename(columns={"index": "datetime"})

    # Ensure the column order roughly matches expectations
    base_cols = ["datetime", "Open", "High", "Low", "Close", "Volume"]
    ordered_cols = [c for c in base_cols if c in df.columns] + [c for c in df.columns if c not in base_cols]
    df = df[ordered_cols]

    return df

def test_indicators():
    """Test MACD and Supertrend calculations"""
    
    print("="*80)
    print("TESTING MACD AND SUPERTREND CALCULATIONS")
    print("="*80)
    
    # Load the CSV data
    print(f"Loading data from: {INDEX_CSV_PATH}")
    try:
        candles = load_nifty_index_csv(INDEX_CSV_PATH)
        print(f"Loaded {len(candles)} rows of data")
        print(f"Date range: {candles['datetime'].min()} to {candles['datetime'].max()}")
    except Exception as e:
        print(f"Error loading CSV: {e}")
        return
    
    # Use a smaller subset for testing (last 1000 rows to ensure we have recent data)
    test_data = candles.tail(1000).copy()
    print(f"\nUsing last 1000 rows for testing")
    print(f"Test data date range: {test_data['datetime'].min()} to {test_data['datetime'].max()}")
    
    # Add indicators
    print(f"\nAdding indicators...")
    try:
        result_df = add_indicators(test_data)
        print(f"Result DataFrame shape: {result_df.shape}")
        print(f"Result columns: {result_df.columns.tolist()}")
        
        # Check for indicator columns
        macd_cols = [col for col in result_df.columns if 'MACD' in col]
        supertrend_cols = [col for col in result_df.columns if 'SUPER' in col]
        
        print(f"\nMACD columns found: {macd_cols}")
        print(f"Supertrend columns found: {supertrend_cols}")
        
        # Show sample calculations
        print("\n" + "="*50)
        print("SAMPLE INDICATOR VALUES (Last 10 rows)")
        print("="*50)
        
        display_cols = ['datetime', 'Close'] + macd_cols + supertrend_cols
        available_cols = [col for col in display_cols if col in result_df.columns]
        
        sample_data = result_df[available_cols].tail(10)
        print(sample_data.to_string(index=False))
        
        # Check for NaN values
        print("\n" + "="*50)
        print("NaN VALUE ANALYSIS")
        print("="*50)
        
        for col in macd_cols + supertrend_cols:
            if col in result_df.columns:
                nan_count = result_df[col].isna().sum()
                total_count = len(result_df)
                print(f"{col}: {nan_count}/{total_count} NaN values ({nan_count/total_count*100:.1f}%)")
        
        # Test signal generation logic
        print("\n" + "="*50)
        print("SIGNAL GENERATION TEST")
        print("="*50)
        
        # Get the last few rows with valid data
        valid_data = result_df.dropna(subset=macd_cols + supertrend_cols).tail(5)
        
        if not valid_data.empty:
            print("Testing signal generation on last 5 valid rows:")
            for _, row in valid_data.iterrows():
                macd = row.get("MACD_12_26_9")
                macds = row.get("MACDs_12_26_9")
                supertd = row.get("SUPERTd_10_3.0")
                close = row.get("Close")
                
                if pd.notna(macd) and pd.notna(macds) and pd.notna(supertd):
                    macd_signal = "buy" if macd > macds else "sell"
                    supertrend_signal = "buy" if supertd == 1 else "sell"
                    final_signal = macd_signal if (macd_signal == supertrend_signal) else None
                    
                    print(f"{row['datetime']}: MACD={macd:.4f}, MACDs={macds:.4f}, SuperTd={supertd}, Close={close:.2f}")
                    print(f"  -> MACD Signal: {macd_signal}, Supertrend Signal: {supertrend_signal}, Final: {final_signal}")
        else:
            print("No valid data found for signal generation test")
            
    except Exception as e:
        print(f"Error adding indicators: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_indicators()
